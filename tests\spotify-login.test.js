// Mock electron modules before importing main
jest.mock('electron', () => ({
  app: {
    getPath: jest.fn().mockReturnValue('/mock/user/data'),
    getVersion: jest.fn().mockReturnValue('1.0.0'),
    whenReady: jest.fn().mockResolvedValue(),
    on: jest.fn(),
    quit: jest.fn()
  },
  BrowserWindow: jest.fn().mockImplementation(() => ({
    loadFile: jest.fn(),
    on: jest.fn(),
    once: jest.fn(),
    setMenu: jest.fn(),
    hide: jest.fn(),
    show: jest.fn(),
    minimize: jest.fn(),
    close: jest.fn(),
    setBounds: jest.fn(),
    getBounds: jest.fn().mockReturnValue({ x: 0, y: 0, width: 1920, height: 1080 }),
    setBrowserView: jest.fn(),
    webContents: {
      on: jest.fn(),
      send: jest.fn()
    }
  })),
  BrowserView: jest.fn().mockImplementation(() => ({
    webContents: {
      loadURL: jest.fn(),
      on: jest.fn(),
      executeJavaScript: jest.fn(),
      isDestroyed: jest.fn().mockReturnValue(false),
      getURL: jest.fn()
    },
    setBounds: jest.fn()
  })),
  Tray: jest.fn(),
  Menu: jest.fn(),
  ipcMain: { on: jest.fn(), handle: jest.fn() },
  screen: {
    getPrimaryDisplay: jest.fn().mockReturnValue({
      workAreaSize: { width: 1920, height: 1080 }
    })
  },
  shell: { openExternal: jest.fn() }
}));

// Mock electron-store
jest.mock('electron-store', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn()
  }));
});

// Mock auto-launch
jest.mock('auto-launch', () => {
  return jest.fn().mockImplementation(() => ({
    isEnabled: jest.fn().mockResolvedValue(false),
    enable: jest.fn().mockResolvedValue(),
    disable: jest.fn().mockResolvedValue()
  }));
});

// Mock fs module
jest.mock('fs');

const fs = require('fs');
const path = require('path');
const { app } = require('electron');

// Create a mock class that only includes the methods we want to test
class MockYSViewerApp {
  constructor() {
    this.spotifyView = null;
  }

  getSpotifyCredentials() {
    try {
      const configPath = path.join(app.getPath('userData'), 'spotify-config.json');

      if (fs.existsSync(configPath)) {
        const configData = fs.readFileSync(configPath, 'utf8');
        const config = JSON.parse(configData);
        return config.credentials || null;
      }

      return null;
    } catch (error) {
      console.error('Error reading Spotify credentials:', error);
      return null;
    }
  }

  saveSpotifyCredentials(username, password) {
    try {
      const configPath = path.join(app.getPath('userData'), 'spotify-config.json');
      const config = {
        credentials: {
          username: username,
          password: password,
          savedAt: new Date().toISOString()
        }
      };

      fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8');
      console.log('Spotify credentials saved to:', configPath);
    } catch (error) {
      console.error('Error saving Spotify credentials:', error);
    }
  }

  async extractSpotifyCredentials() {
    try {
      const credentials = await this.spotifyView.webContents.executeJavaScript(`
        (function() {
          const usernameField = document.querySelector('#login-username');
          const passwordField = document.querySelector('#login-password');

          if (usernameField && passwordField && usernameField.value && passwordField.value) {
            return {
              username: usernameField.value,
              password: passwordField.value
            };
          }

          return null;
        })();
      `);

      return credentials;
    } catch (error) {
      console.error('Error extracting Spotify credentials:', error);
      return null;
    }
  }

  async loginAndPlaySpotify(targetUrl, username, password) {
    try {
      console.log('Attempting Spotify login for user:', username);

      // Load Spotify login page
      this.spotifyView.webContents.loadURL('https://accounts.spotify.com/login');

      // Wait for login page to load
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Fill in login form and submit
      const loginSuccess = await this.spotifyView.webContents.executeJavaScript(`
        (function() {
          try {
            const usernameField = document.querySelector('#login-username');
            const passwordField = document.querySelector('#login-password');
            const loginButton = document.querySelector('#login-button');

            if (!usernameField || !passwordField || !loginButton) {
              console.error('Could not find login form elements');
              return false;
            }

            // Fill in credentials
            usernameField.value = '${username}';
            passwordField.value = '${password}';

            // Trigger events to ensure form validation works
            usernameField.dispatchEvent(new Event('input', { bubbles: true }));
            passwordField.dispatchEvent(new Event('input', { bubbles: true }));

            // Submit form
            loginButton.click();
            return true;
          } catch (error) {
            console.error('Error during login form submission:', error);
            return false;
          }
        })();
      `);

      if (loginSuccess) {
        console.log('Login form submitted, waiting for redirect...');

        // Wait for login to complete and redirect
        await new Promise(resolve => {
          const checkLoginStatus = () => {
            const currentUrl = this.spotifyView.webContents.getURL();
            if (currentUrl.includes('open.spotify.com') || currentUrl.includes('spotify.com/track') || currentUrl.includes('spotify.com/playlist')) {
              console.log('Login successful, redirecting to target URL');
              resolve();
            } else {
              setTimeout(checkLoginStatus, 1000);
            }
          };

          setTimeout(checkLoginStatus, 3000);
        });

        // Navigate to target URL
        this.spotifyView.webContents.loadURL(targetUrl);
      } else {
        console.error('Failed to submit login form');
        this.spotifyView.webContents.loadURL(targetUrl);
      }
    } catch (error) {
      console.error('Error during Spotify login:', error);
      // Fall back to direct URL loading
      this.spotifyView.webContents.loadURL(targetUrl);
    }
  }

  async playSpotifyTrack(url) {
    try {
      if (!this.spotifyView || !this.spotifyView.webContents || this.spotifyView.webContents.isDestroyed()) {
        console.error('Spotify view not available');
        return;
      }

      // Check if we have saved credentials
      const savedCredentials = this.getSpotifyCredentials();

      if (savedCredentials && savedCredentials.username && savedCredentials.password) {
        console.log('Using saved Spotify credentials for automatic login');
        await this.loginAndPlaySpotify(url, savedCredentials.username, savedCredentials.password);
      } else {
        console.log('No saved Spotify credentials, showing login page');
        await this.showSpotifyLoginPage(url);
      }
    } catch (error) {
      console.error('Error playing Spotify track:', error);
    }
  }

  async showSpotifyLoginPage(targetUrl) {
    try {
      // Load Spotify login page
      this.spotifyView.webContents.loadURL('https://accounts.spotify.com/login');

      // Wait for login completion
      return new Promise((resolve) => {
        const checkLoginStatus = async () => {
          try {
            const currentUrl = this.spotifyView.webContents.getURL();

            // Check if we're logged in (redirected to Spotify main page or the target URL)
            if (currentUrl.includes('open.spotify.com') || currentUrl.includes('spotify.com/track') || currentUrl.includes('spotify.com/playlist')) {
              console.log('Spotify login detected, attempting to extract credentials');

              // Try to extract credentials from the login form if still visible
              const credentials = await this.extractSpotifyCredentials();
              if (credentials) {
                this.saveSpotifyCredentials(credentials.username, credentials.password);
              }

              // Navigate to target URL
              this.spotifyView.webContents.loadURL(targetUrl);
              resolve();
            } else {
              // Still on login page, check again in 2 seconds
              setTimeout(checkLoginStatus, 2000);
            }
          } catch (error) {
            console.error('Error checking Spotify login status:', error);
            setTimeout(checkLoginStatus, 2000);
          }
        };

        // Start checking after 3 seconds to allow page to load
        setTimeout(checkLoginStatus, 3000);
      });
    } catch (error) {
      console.error('Error showing Spotify login page:', error);
    }
  }
}

describe('Spotify Login System', () => {
  let appInstance;
  let mockSpotifyView;
  let mockWebContents;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Mock Spotify view and web contents
    mockWebContents = {
      executeJavaScript: jest.fn(),
      isDestroyed: jest.fn().mockReturnValue(false),
      loadURL: jest.fn(),
      getURL: jest.fn()
    };

    mockSpotifyView = {
      webContents: mockWebContents
    };

    // Create app instance with mocked views
    appInstance = new MockYSViewerApp();
    appInstance.spotifyView = mockSpotifyView;
  });

  describe('getSpotifyCredentials', () => {
    test('should return saved credentials when config file exists', () => {
      const mockCredentials = {
        username: '<EMAIL>',
        password: 'testpassword123',
        savedAt: '2023-01-01T00:00:00.000Z'
      };

      const mockConfig = {
        credentials: mockCredentials
      };

      fs.existsSync.mockReturnValue(true);
      fs.readFileSync.mockReturnValue(JSON.stringify(mockConfig));

      const result = appInstance.getSpotifyCredentials();

      expect(result).toEqual(mockCredentials);
      expect(fs.existsSync).toHaveBeenCalledWith('/mock/user/data/spotify-config.json');
      expect(fs.readFileSync).toHaveBeenCalledWith('/mock/user/data/spotify-config.json', 'utf8');
    });

    test('should return null when config file does not exist', () => {
      fs.existsSync.mockReturnValue(false);

      const result = appInstance.getSpotifyCredentials();

      expect(result).toBeNull();
      expect(fs.readFileSync).not.toHaveBeenCalled();
    });

    test('should return null when config file is invalid JSON', () => {
      fs.existsSync.mockReturnValue(true);
      fs.readFileSync.mockReturnValue('invalid json');

      const result = appInstance.getSpotifyCredentials();

      expect(result).toBeNull();
    });

    test('should return null when credentials are missing from config', () => {
      const mockConfig = {
        otherData: 'value'
      };

      fs.existsSync.mockReturnValue(true);
      fs.readFileSync.mockReturnValue(JSON.stringify(mockConfig));

      const result = appInstance.getSpotifyCredentials();

      expect(result).toBeNull();
    });
  });

  describe('saveSpotifyCredentials', () => {
    test('should save credentials to config file in cleartext', () => {
      const username = '<EMAIL>';
      const password = 'testpassword123';

      appInstance.saveSpotifyCredentials(username, password);

      expect(fs.writeFileSync).toHaveBeenCalledWith(
        '/mock/user/data/spotify-config.json',
        expect.stringContaining('"username": "<EMAIL>"'),
        'utf8'
      );

      expect(fs.writeFileSync).toHaveBeenCalledWith(
        '/mock/user/data/spotify-config.json',
        expect.stringContaining('"password": "testpassword123"'),
        'utf8'
      );

      // Verify the saved data structure
      const savedData = JSON.parse(fs.writeFileSync.mock.calls[0][1]);
      expect(savedData.credentials.username).toBe(username);
      expect(savedData.credentials.password).toBe(password);
      expect(savedData.credentials.savedAt).toBeDefined();
    });

    test('should handle file write errors gracefully', () => {
      fs.writeFileSync.mockImplementation(() => {
        throw new Error('Write error');
      });

      // Should not throw
      expect(() => {
        appInstance.saveSpotifyCredentials('user', 'pass');
      }).not.toThrow();
    });
  });

  describe('extractSpotifyCredentials', () => {
    test('should extract credentials from login form', async () => {
      const mockCredentials = {
        username: '<EMAIL>',
        password: 'password123'
      };

      mockWebContents.executeJavaScript.mockResolvedValue(mockCredentials);

      const result = await appInstance.extractSpotifyCredentials();

      expect(result).toEqual(mockCredentials);
      expect(mockWebContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('#login-username')
      );
      expect(mockWebContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('#login-password')
      );
    });

    test('should return null when form fields are not found', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue(null);

      const result = await appInstance.extractSpotifyCredentials();

      expect(result).toBeNull();
    });

    test('should handle JavaScript execution errors', async () => {
      mockWebContents.executeJavaScript.mockRejectedValue(new Error('JS error'));

      const result = await appInstance.extractSpotifyCredentials();

      expect(result).toBeNull();
    });
  });

  describe('loginAndPlaySpotify', () => {
    test('should login with credentials and play target URL', async () => {
      const targetUrl = 'https://open.spotify.com/track/test123';
      const username = '<EMAIL>';
      const password = 'password123';

      // Mock successful login form submission
      mockWebContents.executeJavaScript.mockResolvedValue(true);
      
      // Mock URL changes during login process
      mockWebContents.getURL
        .mockReturnValueOnce('https://accounts.spotify.com/login') // Initial login page
        .mockReturnValueOnce('https://open.spotify.com/'); // After successful login

      const loginPromise = appInstance.loginAndPlaySpotify(targetUrl, username, password);

      // Simulate login completion after a short delay
      setTimeout(() => {
        mockWebContents.getURL.mockReturnValue('https://open.spotify.com/');
      }, 100);

      await loginPromise;

      expect(mockWebContents.loadURL).toHaveBeenCalledWith('https://accounts.spotify.com/login');
      expect(mockWebContents.loadURL).toHaveBeenCalledWith(targetUrl);
      expect(mockWebContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining(username)
      );
      expect(mockWebContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining(password)
      );
    });

    test('should handle login form submission failure', async () => {
      const targetUrl = 'https://open.spotify.com/track/test123';
      const username = '<EMAIL>';
      const password = 'password123';

      // Mock failed login form submission
      mockWebContents.executeJavaScript.mockResolvedValue(false);

      await appInstance.loginAndPlaySpotify(targetUrl, username, password);

      expect(mockWebContents.loadURL).toHaveBeenCalledWith('https://accounts.spotify.com/login');
      expect(mockWebContents.loadURL).toHaveBeenCalledWith(targetUrl);
    });

    test('should handle JavaScript execution errors during login', async () => {
      const targetUrl = 'https://open.spotify.com/track/test123';
      const username = '<EMAIL>';
      const password = 'password123';

      // Mock JavaScript execution error
      mockWebContents.executeJavaScript.mockRejectedValue(new Error('JS error'));

      await appInstance.loginAndPlaySpotify(targetUrl, username, password);

      // Should fall back to direct URL loading
      expect(mockWebContents.loadURL).toHaveBeenCalledWith(targetUrl);
    });
  });

  describe('playSpotifyTrack', () => {
    test('should use saved credentials for automatic login', async () => {
      const targetUrl = 'https://open.spotify.com/track/test123';
      const savedCredentials = {
        username: '<EMAIL>',
        password: 'savedpassword'
      };

      // Mock saved credentials
      fs.existsSync.mockReturnValue(true);
      fs.readFileSync.mockReturnValue(JSON.stringify({ credentials: savedCredentials }));

      // Mock successful login
      mockWebContents.executeJavaScript.mockResolvedValue(true);
      mockWebContents.getURL.mockReturnValue('https://open.spotify.com/');

      // Spy on loginAndPlaySpotify method
      const loginSpy = jest.spyOn(appInstance, 'loginAndPlaySpotify').mockResolvedValue();

      await appInstance.playSpotifyTrack(targetUrl);

      expect(loginSpy).toHaveBeenCalledWith(targetUrl, savedCredentials.username, savedCredentials.password);
    });

    test('should show login page when no saved credentials', async () => {
      const targetUrl = 'https://open.spotify.com/track/test123';

      // Mock no saved credentials
      fs.existsSync.mockReturnValue(false);

      // Spy on showSpotifyLoginPage method
      const loginPageSpy = jest.spyOn(appInstance, 'showSpotifyLoginPage').mockResolvedValue();

      await appInstance.playSpotifyTrack(targetUrl);

      expect(loginPageSpy).toHaveBeenCalledWith(targetUrl);
    });

    test('should handle destroyed Spotify view gracefully', async () => {
      const targetUrl = 'https://open.spotify.com/track/test123';

      // Mock destroyed web contents
      mockWebContents.isDestroyed.mockReturnValue(true);

      // Should not throw
      await expect(appInstance.playSpotifyTrack(targetUrl)).resolves.toBeUndefined();
    });
  });

  describe('Mock Data Collection', () => {
    test('should collect execution data for Spotify login operations', async () => {
      const executionData = [];
      const scenarios = [
        { hasCredentials: true, loginSuccess: true },
        { hasCredentials: true, loginSuccess: false },
        { hasCredentials: false, loginSuccess: true }
      ];

      for (const scenario of scenarios) {
        const startTime = Date.now();
        
        // Setup scenario
        if (scenario.hasCredentials) {
          fs.existsSync.mockReturnValue(true);
          fs.readFileSync.mockReturnValue(JSON.stringify({
            credentials: { username: 'test', password: 'test' }
          }));
        } else {
          fs.existsSync.mockReturnValue(false);
        }

        mockWebContents.executeJavaScript.mockResolvedValue(scenario.loginSuccess);
        mockWebContents.getURL.mockReturnValue(
          scenario.loginSuccess ? 'https://open.spotify.com/' : 'https://accounts.spotify.com/login'
        );

        try {
          await appInstance.playSpotifyTrack('https://open.spotify.com/track/test');
          const endTime = Date.now();
          
          executionData.push({
            scenario: scenario,
            success: true,
            executionTime: endTime - startTime
          });
        } catch (error) {
          const endTime = Date.now();
          
          executionData.push({
            scenario: scenario,
            success: false,
            executionTime: endTime - startTime,
            error: error.message
          });
        }
      }

      // Verify we collected data for all scenarios
      expect(executionData).toHaveLength(3);
      
      console.log('Spotify login execution data:', {
        totalExecutions: executionData.length,
        successfulExecutions: executionData.filter(data => data.success).length,
        averageExecutionTime: executionData.reduce((sum, data) => sum + data.executionTime, 0) / executionData.length,
        scenarios: executionData
      });
    });
  });
});
